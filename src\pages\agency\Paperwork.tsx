import React, { useState, useEffect } from 'react';
import { format, isAfter, isBefore, isEqual } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { FileText, Download, Eye, Search, RotateCcw, CalendarIcon, Upload, Plus, MoreVertical, Trash2, Book, ChevronLeft, ChevronRight, Loader2, User } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/core/api/supabase';
import { getDocumentArtistDetails } from '@/features/documents/api/documentHelpers';
import { getProfileById } from '@/features/entities/api/profileHelpers';
import { getUserDocuments } from '@/features/documents/api/documentHelpers';
import { useAuth } from '@/contexts/AuthContext';
import DocumentUploadModal from '@/components/documents/DocumentUploadModal';
import DocumentGenerateModal from '@/components/documents/DocumentGenerateModal';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { DeleteConfirmationDialog } from '@/components/ui/delete-confirmation-dialog';
import { toast } from 'sonner';
import {
  cn,
  useToast,
  useIsMobile,
  showSuccessToast,
  showErrorToast,
  showUndoToast,
  getDocumentStatus,
  getStatusBadgeVariant
} from '@/core';

interface DocumentItem {
  id: string;
  name: string;
  artist: string;
  date: string;
  booking_date?: string;
  status: string;
  type: string;
  description?: string;
  file_url?: string;
  booking_id?: string;
  booking_title?: string;
  signed_by_artist?: boolean;
  signed_by_venue?: boolean;
}

const AgencyPaperwork = () => {
  const { profile } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { toast } = useToast();

  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [timeFilter, setTimeFilter] = useState('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [documentGenerateModalOpen, setDocumentGenerateModalOpen] = useState(false);
  const [documentType, setDocumentType] = useState('contract');
  const [selectedBookingId, setSelectedBookingId] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const {
    data: paperworkItems = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['agencyPaperwork'],
    queryFn: async () => {
      try {
        if (!profile) throw new Error('Not authenticated');

        const documents = await getUserDocuments(profile.id, 'agency');
        if (!documents) return [];

        const documentsWithArtistInfo: DocumentItem[] = await Promise.all(documents.map(async doc => {
          if (!doc.bookings?.artist_id) {
            return {
              id: doc.id,
              name: doc.title,
              artist: "Unknown Artist",
              date: doc.created_at,
              booking_date: doc.bookings?.booking_start,
              status: getDocumentStatus(doc),
              file_url: doc.file_url,
              description: doc.description || '',
              type: doc.document_type,
              booking_id: doc.booking_id,
              booking_title: doc.bookings?.title,
              signed_by_artist: doc.signed_by_artist,
              signed_by_venue: doc.signed_by_venue
            };
          }

          const artistDetails = await getDocumentArtistDetails(doc.bookings.artist_id);
          const profileData = await getProfileById(doc.bookings.artist_id);
          return {
            id: doc.id,
            name: doc.title,
            artist: artistDetails?.artist_name || profileData?.name || 'Unknown Artist',
            date: doc.created_at,
            booking_date: doc.bookings?.booking_start,
            status: getDocumentStatus(doc),
            file_url: doc.file_url,
            description: doc.description || '',
            type: doc.document_type,
            booking_id: doc.booking_id,
            booking_title: doc.bookings?.title,
            signed_by_artist: doc.signed_by_artist,
            signed_by_venue: doc.signed_by_venue
          };
        }));
        return documentsWithArtistInfo;
      } catch (err) {
        console.error('Error fetching paperwork:', err);
        showErrorToast("Error loading documents");
        return [];
      }
    },
    enabled: !!profile
  });

  const {
    data: bookings = []
  } = useQuery({
    queryKey: ['agencyBookings'],
    queryFn: async () => {
      try {
        if (!profile) return [];

        ('Fetching agency entities for user:', profile.id);

        // Get user's agency entities from entity_users table
        const {
          data: agencyEntities,
          error: entityError
        } = await supabase
          .from('entity_users')
          .select('entity_id')
          .eq('user_id', profile.id);

        ('Entity query error:', entityError);
        ('Agency entities found:', agencyEntities);

        if (!agencyEntities || agencyEntities.length === 0) return [];

        const {
          data: bookingsData,
          error
        } = await supabase
          .from('bookings')
          .select('id, title, artist_id, booking_start, venue_id')
          .in('owner_entity_id', agencyEntities.map(v => v.entity_id))
          .order('booking_start', {
            ascending: false
          });

        if (error) throw error;
        ('Agency bookings found:', bookingsData?.length || 0);
        return bookingsData || [];
      } catch (err) {
        console.error('Error fetching bookings:', err);
        return [];
      }
    },
    enabled: !!profile
  });

  const filteredItems = paperworkItems.filter(item => {
    if (statusFilter !== 'all' && item.status.toLowerCase() !== statusFilter.toLowerCase()) return false;
    if (typeFilter !== 'all' && item.type.toLowerCase() !== typeFilter.toLowerCase()) return false;
    if (item.booking_date) {
      const bookingDate = new Date(item.booking_date);
      if (timeFilter === 'future' && isBefore(bookingDate, new Date())) return false;
      if (timeFilter === 'past' && (isAfter(bookingDate, new Date()) || isEqual(bookingDate, new Date()))) return false;
    }
    if (dateRange?.from && item.booking_date && isBefore(new Date(item.booking_date), dateRange.from)) return false;
    if (dateRange?.to && item.booking_date && isAfter(new Date(item.booking_date), dateRange.to)) return false;
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const descriptionMatch = item.description ? item.description.toLowerCase().includes(query) : false;
      return item.name.toLowerCase().includes(query) || item.artist.toLowerCase().includes(query) || descriptionMatch;
    }
    return true;
  });

  const indexOfLastItem = currentPage * rowsPerPage;
  const indexOfFirstItem = indexOfLastItem - rowsPerPage;
  const currentItems = filteredItems.slice(indexOfFirstItem, indexOfLastItem);
  const pageCount = Math.ceil(filteredItems.length / rowsPerPage);

  const handlePageChange = (page: number) => {
    if (page > 0 && page <= pageCount) {
      setCurrentPage(page);
    }
  };

  const resetFilters = () => {
    setSearchQuery('');
    setStatusFilter('all');
    setTypeFilter('all');
    setTimeFilter('all');
    setDateRange({
      from: undefined,
      to: undefined
    });
    setCurrentPage(1);
  };

  const handleUploadDocument = (bookingId?: string) => {
    setSelectedBookingId(bookingId || null);
    setUploadModalOpen(true);
  };

  const handleGenerateDocument = (type: string = 'contract', bookingId?: string) => {
    setSelectedBookingId(bookingId || null);
    setDocumentType(type);
    setDocumentGenerateModalOpen(true);
  };

  const handleViewDocument = (item: DocumentItem) => {
    navigate(`/documents/${item.id}`);
  };

  const handleDownloadDocument = (item: DocumentItem) => {
    if (item.type !== 'contract' && item.file_url) {
      const link = document.createElement('a');
      link.href = item.file_url;
      link.download = item.name || 'document';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      showErrorToast("Download not available");
    }
  };

  const handleViewBooking = (bookingId?: string) => {
    if (bookingId) {
      navigate(`/agency/bookings/${bookingId}`);
    } else {
      showErrorToast("Booking not available");
    }
  };

  const handleDeleteDocument = (documentId: string) => {
    setDocumentToDelete(documentId);
    setShowDeleteDialog(true);
  };

  const confirmDeleteDocument = async () => {
    if (!documentToDelete) return;

    setIsDeleting(true);
    try {
      const { data: docCheck, error: checkError } = await supabase
        .from('documents')
        .select('id, file_url, booking_id')
        .eq('id', documentToDelete)
        .single();

      if (checkError) {
        console.error('Error checking document:', checkError);
        throw new Error("Document not found");
      }

      const { error } = await supabase
        .from('documents')
        .delete()
        .eq('id', documentToDelete);

      if (error) {
        console.error('Error deleting document from database:', error);
        throw error;
      }

      if (docCheck.file_url && docCheck.booking_id) {
        try {
          const filePath = docCheck.file_url.split('/').pop();
          if (filePath) {
            await supabase.storage
              .from('documents')
              .remove([`${docCheck.booking_id}/${filePath}`]);
          }
        } catch (storageError) {
          console.error('Error deleting file from storage:', storageError);
        }
      }

      await refetch();

      showSuccessToast("Document deleted successfully");
    } catch (err) {
      console.error('Error deleting document:', err);
      showErrorToast("Failed to delete document");
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
      setDocumentToDelete(null);
    }
  };

  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'contract':
        return <FileText className="h-4 w-4 mr-2 text-purple-500" />;
      case 'rider':
        return <FileText className="h-4 w-4 mr-2 text-orange-500" />;
      case 'callsheet':
        return <FileText className="h-4 w-4 mr-2 text-green-500" />;
      default:
        return <FileText className="h-4 w-4 mr-2 text-blue-500" />;
    }
  };

  return (
    <DashboardLayout userType="agency">
      <div className="space-y-6">
        <div className="space-y-4">
          {/* Top row with title */}
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold tracking-tight">Paperwork</h1>
            {/* Desktop buttons - hidden on mobile */}
            <div className="hidden sm:flex gap-2">
              <Button disabled={bookings.length === 0} className="bg-stagecloud-purple hover:bg-stagecloud-purple/90 bg-stagecloud-black" onClick={() => handleGenerateDocument()}>
                <FileText className="h-4 w-4 mr-2" />
                New Document
              </Button>
              <Button onClick={() => handleUploadDocument()} variant="outline" disabled={bookings.length === 0}>
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
            </div>
          </div>

          {/* Mobile buttons - shown only on mobile */}
          <div className="flex sm:hidden gap-2">
            <Button disabled={bookings.length === 0} className="flex-1 bg-stagecloud-purple hover:bg-stagecloud-purple/90 bg-stagecloud-black" onClick={() => handleGenerateDocument()}>
              <FileText className="h-4 w-4 mr-2" />
              New Document
            </Button>
            <Button onClick={() => handleUploadDocument()} variant="outline" disabled={bookings.length === 0} className="flex-1">
              <Upload className="h-4 w-4 mr-2" />
              Upload Document
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-6">
              {isMobile ? (
                <div className="space-y-3">
                  {/* Search bar - full width on mobile */}
                  <div className="relative w-full">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search documents or artists..."
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      className="pl-8 w-full"
                    />
                  </div>

                  {/* Filter controls in a grid */}
                  <div className="grid grid-cols-2 gap-2">
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All statuses</SelectItem>
                        <SelectItem value="signed">Signed</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={typeFilter} onValueChange={setTypeFilter}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Document Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All types</SelectItem>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="rider">Rider</SelectItem>
                        <SelectItem value="callsheet">Call Sheet</SelectItem>
                        <SelectItem value="invoice">Invoice</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={timeFilter} onValueChange={setTimeFilter}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Booking time" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All bookings</SelectItem>
                        <SelectItem value="future">Future bookings</SelectItem>
                        <SelectItem value="past">Past bookings</SelectItem>
                      </SelectContent>
                    </Select>

                    <div className="flex space-x-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className="flex-1 justify-start text-left font-normal text-xs h-10 truncate">
                            <CalendarIcon className="mr-1 h-3.5 w-3.5 flex-shrink-0" />
                            <span className="truncate">
                              {dateRange?.from ? dateRange.to ?
                                `${format(dateRange.from, "LLL dd")} - ${format(dateRange.to, "LLL dd")}` :
                                format(dateRange.from, "LLL dd, y") : "Date range"}
                            </span>
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar initialFocus mode="range" defaultMonth={dateRange?.from} selected={dateRange} onSelect={setDateRange} numberOfMonths={1} className={cn("p-3 pointer-events-auto")} />
                        </PopoverContent>
                      </Popover>

                      <Button variant="outline" onClick={resetFilters} size="icon" className="h-10 w-10 flex-shrink-0" title="Reset Filters">
                        <RotateCcw className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-wrap gap-3 items-center">
                  <div className="relative flex-1 min-w-[200px]">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="Search documents or artists..." value={searchQuery} onChange={e => setSearchQuery(e.target.value)} className="pl-8" />
                  </div>

                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All statuses</SelectItem>
                      <SelectItem value="signed">Signed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Document Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All types</SelectItem>
                      <SelectItem value="contract">Contract</SelectItem>
                      <SelectItem value="rider">Rider</SelectItem>
                      <SelectItem value="callsheet">Call Sheet</SelectItem>
                      <SelectItem value="invoice">Invoice</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={timeFilter} onValueChange={setTimeFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Booking time" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All bookings</SelectItem>
                      <SelectItem value="future">Future bookings</SelectItem>
                      <SelectItem value="past">Past bookings</SelectItem>
                    </SelectContent>
                  </Select>

                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-auto justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateRange?.from ? dateRange.to ? <>
                              {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                            </> : format(dateRange.from, "LLL dd, y") : "Booking date range"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar initialFocus mode="range" defaultMonth={dateRange?.from} selected={dateRange} onSelect={setDateRange} numberOfMonths={2} className={cn("p-3 pointer-events-auto")} />
                    </PopoverContent>
                  </Popover>

                  <Button variant="outline" onClick={resetFilters} size="icon" className="h-10 w-10" title="Reset Filters">
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            <div>
              {isMobile ? (
                <div className="flex flex-col space-y-3">
                  {isLoading ? (
                    <div className="flex justify-center items-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                      <span className="ml-2 text-sm text-gray-600">Loading...</span>
                    </div>
                  ) : currentItems.length > 0 ? (
                      currentItems.map(item => (
                        <div
                          key={item.id}
                          className="border rounded-lg p-3 cursor-pointer hover:bg-muted/50 transition-colors shadow-sm relative overflow-hidden"
                          onClick={() => handleViewDocument(item)}
                        >
                          {/* Status indicator strip */}
                          <div className={`absolute top-0 left-0 w-1 h-full
                            ${item.status === 'Signed' ? 'bg-green-500' :
                              item.status === 'Pending' ? 'bg-yellow-500' :
                              'bg-gray-400'}`}
                          ></div>

                          {/* Document header with type and status */}
                          <div className="flex justify-between items-center mb-2 pl-2">
                            <div className="flex items-center">
                              {getDocumentIcon(item.type)}
                              <span className="text-xs font-medium capitalize">{item.type}</span>
                            </div>
                            <Badge variant={getStatusBadgeVariant(item.status)}>
                              {item.status}
                            </Badge>
                          </div>

                          {/* Document title */}
                          <h4 className="font-medium text-sm mb-1 pl-2">{item.name}</h4>

                          {/* Artist name */}
                          <div className="flex items-center text-xs text-gray-600 mb-1 pl-2">
                            <User className="h-3 w-3 mr-1.5" />
                            <span>{item.artist}</span>
                          </div>

                          {/* Date */}
                          <div className="flex items-center text-xs text-gray-600 pl-2">
                            <Calendar className="h-3 w-3 mr-1.5" />
                            <span>{format(new Date(item.date), "MMM d, yyyy")}</span>
                          </div>

                          {/* Actions */}
                          <div className="flex justify-end mt-2">
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteDocument(item.id);
                            }}>
                              <Trash2 className="h-4 w-4 text-gray-500" />
                            </Button>
                          </div>
                        </div>
                      ))
                  ) : (
                    <div className="text-center py-6 bg-gray-50 rounded-lg">
                      <p className="text-gray-500 text-sm">
                        {error ? 'Error loading documents' : 'No documents found'}
                      </p>
                      {(searchQuery || statusFilter !== 'all' || typeFilter !== 'all' || timeFilter !== 'all' || dateRange?.from) && (
                        <Button variant="outline" onClick={resetFilters} className="mt-3 text-xs py-1 h-8">
                          <RotateCcw className="mr-1.5 h-3 w-3" />
                          Reset Filters
                        </Button>
                      )}
                    </div>
                  )}

                  {filteredItems.length > rowsPerPage && (
                    <div className="w-full flex justify-between items-center mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="px-2"
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>

                      <span className="text-sm">
                        Page {currentPage} of {pageCount || 1}
                      </span>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === pageCount || pageCount === 0}
                        className="px-2"
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                  <>
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Document</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Artist</TableHead>
                            <TableHead>Document Date</TableHead>
                            <TableHead>Booking Date</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {isLoading ? <TableRow>
                              <TableCell colSpan={7} className="text-center py-8">
                                Loading documents...
                              </TableCell>
                            </TableRow> : currentItems.length > 0 ? currentItems.map(item => <TableRow key={item.id} className="hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => handleViewDocument(item)}>
                                <TableCell className="font-medium">
                                  <div className="flex items-center">
                                    {getDocumentIcon(item.type)}
                                    <span>
                                      {item.name}
                                    </span>
                                  </div>
                                </TableCell>
                                <TableCell className="capitalize">{item.type}</TableCell>
                                <TableCell>{item.artist}</TableCell>
                                <TableCell>{new Date(item.date).toLocaleDateString()}</TableCell>
                                <TableCell>
                                  {item.booking_date ? new Date(item.booking_date).toLocaleDateString() : 'N/A'}
                                </TableCell>
                                <TableCell>
                                  <Badge variant={getStatusBadgeVariant(item.status)}>
                                    {item.status}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-right">
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                        <span className="sr-only">Open menu</span>
                                        <MoreVertical className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem onClick={(e) => {
                                        e.stopPropagation();
                                        handleViewDocument(item);
                                      }}>
                                        <Eye className="mr-2 h-4 w-4" />
                                        <span>View Document</span>
                                      </DropdownMenuItem>
                                      {item.booking_id && (
                                        <DropdownMenuItem onClick={(e) => {
                                          e.stopPropagation();
                                          handleViewBooking(item.booking_id);
                                        }}>
                                          <Book className="mr-2 h-4 w-4" />
                                          <span>View Booking</span>
                                        </DropdownMenuItem>
                                      )}
                                      <DropdownMenuItem onClick={(e) => {
                                        e.stopPropagation();
                                        handleDownloadDocument(item);
                                      }}>
                                        <Download className="mr-2 h-4 w-4" />
                                        <span>Download</span>
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem className="text-red-600" onClick={(e) => {
                                        e.stopPropagation();
                                        handleDeleteDocument(item.id);
                                      }}>
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        <span>Delete</span>
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </TableCell>
                              </TableRow>) : <TableRow>
                              <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                                {error ? 'Error loading documents' : 'No documents found matching your filters'}
                              </TableCell>
                            </TableRow>}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Pagination for desktop */}
                    {filteredItems.length > rowsPerPage && (
                      <div className="flex items-center justify-end space-x-2 py-4">
                        <div className="flex-1 text-sm text-muted-foreground">
                          Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to <span className="font-medium">{Math.min(indexOfLastItem, filteredItems.length)}</span> of <span className="font-medium">{filteredItems.length}</span> documents
                        </div>
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"} />
                            </PaginationItem>
                            {Array.from({ length: Math.min(5, pageCount) }, (_, i) => {
                              // Show first page, last page, current page, and pages around current
                              let pageNum = i + 1;
                              if (pageCount > 5) {
                                if (currentPage <= 3) {
                                  // Near start, show first 5 pages
                                  pageNum = i + 1;
                                } else if (currentPage >= pageCount - 2) {
                                  // Near end, show last 5 pages
                                  pageNum = pageCount - 4 + i;
                                } else {
                                  // In middle, show current and surrounding pages
                                  pageNum = currentPage - 2 + i;
                                }
                              }
                              return (
                                <PaginationItem key={pageNum}>
                                  <PaginationLink
                                    onClick={() => handlePageChange(pageNum)}
                                    isActive={currentPage === pageNum}
                                  >
                                    {pageNum}
                                  </PaginationLink>
                                </PaginationItem>
                              );
                            })}
                            <PaginationItem>
                              <PaginationNext onClick={() => handlePageChange(currentPage + 1)} className={currentPage === pageCount ? "pointer-events-none opacity-50" : "cursor-pointer"} />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </>
                )
  }
            </div>
          </CardContent>
        </Card>
      </div>

      <DocumentUploadModal
        open={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        bookingId={selectedBookingId || undefined}
        onSuccess={() => refetch()}
      />

      <DocumentGenerateModal
        open={documentGenerateModalOpen}
        onClose={() => setDocumentGenerateModalOpen(false)}
        bookingId={selectedBookingId || undefined}
        onSuccess={() => refetch()}
      />

      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={confirmDeleteDocument}
        title="Delete Document"
        description="Are you sure you want to delete this document? This action cannot be undone."
        itemType="document"
        isDeleting={isDeleting}
      />
    </DashboardLayout>
  );
};

export default AgencyPaperwork;
