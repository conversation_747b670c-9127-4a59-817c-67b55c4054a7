import { useState, useEffect, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Download, CheckCircle2, File, Calendar, MapPin, UserCheck, FileText, Share2, ExternalLink, Loader2, Menu, AlertTriangle } from 'lucide-react';
import { supabase } from '@/core/api/supabase';
import {
  getDocumentById,
  toggleDocumentSignature,
  generateDocumentShareId,
  getDocumentArtistDetails,
  getDocumentVenueDetails,
  debugDocumentAccess
} from '@/features/documents/api/documentHelpers';
import DocumentShareModal from './DocumentShareModal';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useAuth } from '@/contexts/AuthContext';
import { format, differenceInHours, differenceInMinutes } from 'date-fns';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { useIsMobile, useToast, calculateTotalPrice, formatCurrencyEU, getDocumentStatus } from '@/core';
import html2pdf from 'html2pdf.js';

interface DocumentViewerProps {
  standalone?: boolean;
}

const DocumentViewer = ({ standalone = true }: DocumentViewerProps) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { profile, isArtist, isVenue } = useAuth();
  const [document, setDocument] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [signing, setSigning] = useState(false);
  const [showSignDialog, setShowSignDialog] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const userType = profile?.user_type as 'artist' | 'venue';
  const isMobile = useIsMobile();

  useEffect(() => {
    if (id) {
      loadDocument(id);
    }
  }, [id]);

  const loadDocument = async (documentId: string) => {
    setLoading(true);
    try {
      const documentData = await getDocumentById(documentId);

      if (!documentData) {
        console.error('Document not found, checking access rights...');

        // Debug document access if the user is logged in
        if (profile?.id) {
          const { data: accessData, error: accessError } = await debugDocumentAccess(documentId, profile.id);

          if (!accessError && accessData && Array.isArray(accessData)) {
            console.log('Document access debug results:', accessData);

            // Type assertion to help TypeScript understand the structure
            const typedAccessData = accessData as Array<{ check_name: string, has_access: boolean }>;

            // Check if the document exists but user doesn't have access
            const documentExists = typedAccessData.find(check => check.check_name === 'document_exists')?.has_access;
            const hasAccess = typedAccessData.find(check => check.check_name === 'has_access')?.has_access;

            if (documentExists && !hasAccess) {
              toast({
                title: "Access Denied",
                description: "You don't have permission to view this document.",
                variant: "destructive",
              });
            } else if (!documentExists) {
              toast({
                title: "Document not found",
                description: "The requested document could not be found.",
                variant: "destructive",
              });
            }
          } else {
            console.error('Error debugging document access:', accessError);
            toast({
              title: "Document not found",
              description: "The requested document could not be found.",
              variant: "destructive",
            });
          }
        } else {
          toast({
            title: "Document not found",
            description: "The requested document could not be found.",
            variant: "destructive",
          });
        }

        if (standalone) {
          navigate(-1);
        }
        return;
      }

      // If the document has a booking, fetch artist and venue details
      if (documentData.booking_id) {
        const bookingResult = await supabase
          .from('bookings')
          .select('artist_id, venue_id')
          .eq('id', documentData.booking_id)
          .single();

        if (!bookingResult.error && bookingResult.data) {
          const booking = bookingResult.data;

          // Fetch artist details
          if (booking.artist_id) {
            const artistDetails = await getDocumentArtistDetails(booking.artist_id);
            if (artistDetails) {
              // Use type assertion to add properties to documentData
              (documentData as any).artist_name = artistDetails.artist_name;
              (documentData as any).artist_id = booking.artist_id;
            }
          }

          // Fetch venue details
          if (booking.venue_id) {
            const venueDetails = await getDocumentVenueDetails(booking.venue_id);
            if (venueDetails) {
              // Use type assertion to add properties to documentData
              (documentData as any).venue_name = (venueDetails as any).venue_name;
              (documentData as any).venue_address = (venueDetails as any).address;
              (documentData as any).venue_id = booking.venue_id;
            }
          }
        }
      }

      setDocument(documentData);
    } catch (error) {
      console.error("Error loading document:", error);
      toast({
        title: "Error loading document",
        description: "There was a problem retrieving the document.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSignDocument = () => {
    if (!profile || !document) return;
    setShowSignDialog(true);
  };

  const confirmSignDocument = async () => {
    if (!profile || !document) return;

    setSigning(true);
    try {
      const result = await toggleDocumentSignature(document.id, profile.id, userType);

      if (result.error) {
        throw result.error;
      }

      await loadDocument(document.id);

      toast({
        title: "Document signed",
        description: "The document has been successfully signed.",
      });
    } catch (error) {
      console.error("Error signing document:", error);
      toast({
        title: "Signing failed",
        description: "There was a problem signing the document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSigning(false);
      setShowSignDialog(false);
    }
  };

  const handleDownload = () => {
    // Rename our document variable to avoid conflict with the global document object
    const docData = document;

    if (docData?.file_url) {
      // For uploaded files, just open the URL
      window.open(docData.file_url, '_blank');
    } else if (docData?.content) {
      // For generated documents, create a PDF
      try {
        // Create a temporary div to hold the document content
        const element = window.document.createElement('div');
        element.className = 'document-for-pdf';
        element.style.padding = '20px';
        element.style.fontFamily = 'Arial, sans-serif';
        element.style.maxWidth = '800px';
        element.style.margin = '0 auto';

        // Add document content with proper styling
        const contentDiv = window.document.createElement('div');
        contentDiv.className = 'document-content';

        // Apply styles to ensure rich text formatting is preserved
        contentDiv.style.fontSize = '12pt';
        contentDiv.style.lineHeight = '1.5';
        contentDiv.style.color = '#000';

        // Add the content based on its type
        if (typeof docData.content === 'string') {
          // For HTML content, we need to preserve the formatting
          contentDiv.innerHTML = docData.content;

          // Apply styles to common HTML elements to ensure proper formatting
          const styleElement = window.document.createElement('style');
          styleElement.textContent = `
            .document-for-pdf h1 { font-size: 18pt; margin-bottom: 12pt; font-weight: bold; }
            .document-for-pdf h2 { font-size: 16pt; margin-bottom: 10pt; font-weight: bold; }
            .document-for-pdf h3 { font-size: 14pt; margin-bottom: 8pt; font-weight: bold; }
            .document-for-pdf p { margin-bottom: 10pt; }
            .document-for-pdf ul, .document-for-pdf ol { margin-left: 20pt; margin-bottom: 10pt; }
            .document-for-pdf li { margin-bottom: 5pt; }
            .document-for-pdf table { width: 100%; border-collapse: collapse; margin-bottom: 15pt; }
            .document-for-pdf th, .document-for-pdf td { border: 1px solid #ddd; padding: 8pt; text-align: left; }
            .document-for-pdf th { background-color: #f2f2f2; font-weight: bold; }
            .document-for-pdf a { color: #0066cc; text-decoration: underline; }
            .document-for-pdf blockquote { margin-left: 20pt; padding-left: 10pt; border-left: 3pt solid #ddd; font-style: italic; }
          `;
          element.appendChild(styleElement);
        } else if (typeof docData.content === 'object') {
          // Handle legacy format
          contentDiv.innerHTML = docData.content.terms || docData.content.details || docData.content.requirements || '';
        }

        // Add the content directly to the element (no title)
        element.appendChild(contentDiv);

        // Add signatures if document is signable
        if (docData.is_signable) {
          const signaturesDiv = window.document.createElement('div');
          signaturesDiv.style.marginTop = '40px';
          signaturesDiv.style.paddingTop = '20px';
          signaturesDiv.style.borderTop = '1px solid #ccc';

          const signaturesTitle = window.document.createElement('h2');
          signaturesTitle.style.textAlign = 'center';
          signaturesTitle.style.marginBottom = '20px';
          signaturesTitle.textContent = 'Signatures';
          signaturesDiv.appendChild(signaturesTitle);

          const signaturesContainer = window.document.createElement('div');
          signaturesContainer.style.display = 'flex';
          signaturesContainer.style.justifyContent = 'space-between';

          // Venue signature
          const venueSignature = window.document.createElement('div');
          venueSignature.style.width = '48%';
          venueSignature.style.borderRight = '1px solid #ccc';
          venueSignature.style.paddingRight = '20px';

          const venueTitle = window.document.createElement('p');
          venueTitle.style.fontWeight = 'bold';
          venueTitle.style.marginBottom = '10px';
          venueTitle.textContent = 'For the Venue:';
          venueSignature.appendChild(venueTitle);

          if (docData.signed_by_venue) {
            const venueSignedText = window.document.createElement('p');
            venueSignedText.style.fontStyle = 'italic';
            venueSignedText.style.color = '#16a34a';
            venueSignedText.textContent = 'Signed electronically';
            venueSignature.appendChild(venueSignedText);

            if (docData.venue_signature_date) {
              const venueDate = window.document.createElement('p');
              venueDate.style.fontSize = '0.875rem';
              venueDate.style.color = '#6b7280';
              venueDate.textContent = format(new Date(docData.venue_signature_date), 'MMMM d, yyyy');
              venueSignature.appendChild(venueDate);
            }
          } else {
            const venueNotSigned = window.document.createElement('p');
            venueNotSigned.style.fontStyle = 'italic';
            venueNotSigned.style.color = '#9ca3af';
            venueNotSigned.textContent = 'Not yet signed';
            venueSignature.appendChild(venueNotSigned);
          }

          signaturesContainer.appendChild(venueSignature);

          // Artist signature
          const artistSignature = window.document.createElement('div');
          artistSignature.style.width = '48%';
          artistSignature.style.paddingLeft = '20px';

          const artistTitle = window.document.createElement('p');
          artistTitle.style.fontWeight = 'bold';
          artistTitle.style.marginBottom = '10px';
          artistTitle.textContent = 'For the Artist:';
          artistSignature.appendChild(artistTitle);

          if (docData.signed_by_artist) {
            const artistSignedText = window.document.createElement('p');
            artistSignedText.style.fontStyle = 'italic';
            artistSignedText.style.color = '#16a34a';
            artistSignedText.textContent = 'Signed electronically';
            artistSignature.appendChild(artistSignedText);

            if (docData.artist_signature_date) {
              const artistDate = window.document.createElement('p');
              artistDate.style.fontSize = '0.875rem';
              artistDate.style.color = '#6b7280';
              artistDate.textContent = format(new Date(docData.artist_signature_date), 'MMMM d, yyyy');
              artistSignature.appendChild(artistDate);
            }
          } else {
            const artistNotSigned = window.document.createElement('p');
            artistNotSigned.style.fontStyle = 'italic';
            artistNotSigned.style.color = '#9ca3af';
            artistNotSigned.textContent = 'Not yet signed';
            artistSignature.appendChild(artistNotSigned);
          }

          signaturesContainer.appendChild(artistSignature);
          signaturesDiv.appendChild(signaturesContainer);
          element.appendChild(signaturesDiv);
        }

        // Append to document temporarily
        window.document.body.appendChild(element);

        // Generate PDF with better options for rich text
        const opt = {
          margin: [15, 15, 15, 15], // top, right, bottom, left margins in mm
          filename: `${docData.title.replace(/\s+/g, '_')}.pdf`,
          image: { type: 'jpeg', quality: 1 },
          html2canvas: {
            scale: 2, // Higher scale for better quality
            useCORS: true, // Enable CORS for images
            logging: false,
            letterRendering: true,
            allowTaint: true
          },
          jsPDF: {
            unit: 'mm',
            format: 'a4',
            orientation: 'portrait',
            compress: true
          }
        };

        html2pdf().from(element).set(opt).save();

        // Remove the temporary element
        setTimeout(() => {
          window.document.body.removeChild(element);
        }, 100);

        toast({
          title: "Download started",
          description: "Your document is being prepared for download.",
        });
      } catch (error) {
        console.error('Error generating PDF:', error);
        toast({
          title: "Download failed",
          description: "There was a problem generating the PDF. Please try again.",
          variant: "destructive",
        });
      }
    } else {
      toast({
        title: "Download unavailable",
        description: "This document cannot be downloaded.",
        variant: "default",
      });
    }
  };

  const handleViewBooking = () => {
    if (document?.booking_id) {
      // Redirect to the appropriate entity-specific booking details page
      if (userType === 'artist') {
        navigate(`/artist/bookings/${document.booking_id}`);
      } else if (userType === 'venue') {
        navigate(`/venue/bookings/${document.booking_id}`);
      } else if (userType === 'agency') {
        navigate(`/agency/bookings/${document.booking_id}`);
      } else {
        // Fallback to the user's dashboard if userType is unknown
        navigate(`/${userType}/dashboard`);
      }
    } else {
      toast({
        title: "Booking not available",
        description: "No booking associated with this document.",
        variant: "destructive",
      });
    }
  };

  const getDocumentStatusBadgeProps = (document: any) => {
    // Only calculate special statuses for signable documents
    if (document.is_signable) {
      const status = getDocumentStatus(document).toLowerCase();
      let variant: 'outline' | 'secondary' | 'default' = 'default';

      switch (status) {
        case 'signed':
          variant = 'outline';
          break;
        case 'pending':
          variant = 'secondary';
          break;
      }

      return {
        variant,
        children: status
      };
    } else {
      // For non-signable documents, just show the status as is
      return {
        variant: 'default' as const,
        children: document.status || 'generated'
      };
    }
  };

  const renderDocumentContent = () => {
    if (!document) return null;

    // Document content debugging is complete, we can remove these logs

    // Check if content is a string (rich text HTML) - this applies to all document types
    if (document.content && typeof document.content === 'string') {
      return (
        <div className="document-content p-6 bg-white">
          <div className="prose prose-sm max-w-none" dangerouslySetInnerHTML={{ __html: document.content }} />

          {/* Only show signatures section for signable documents */}
          {document.is_signable && (
            <div className="mt-12 pt-8 border-t">
              <h2 className="text-lg font-semibold mb-6 text-center">Signatures</h2>
              <div className="flex justify-between">
                <div className="w-1/2 pr-4 border-r">
                  <p className="font-semibold mb-2">For the Venue:</p>
                  {document.signed_by_venue ? (
                    <div>
                      <p className="italic text-green-600 flex items-center">
                        <CheckCircle2 className="h-4 w-4 mr-1" />
                        Signed electronically
                      </p>
                      <p className="text-sm text-gray-500">
                        {document.venue_signature_date ? format(new Date(document.venue_signature_date), "MMMM d, yyyy") : ""}
                      </p>
                    </div>
                  ) : (
                    <p className="italic text-gray-400">Not yet signed</p>
                  )}
                </div>
                <div className="w-1/2 pl-4">
                  <p className="font-semibold mb-2">For the Artist:</p>
                  {document.signed_by_artist ? (
                    <div>
                      <p className="italic text-green-600 flex items-center">
                        <CheckCircle2 className="h-4 w-4 mr-1" />
                        Signed electronically
                      </p>
                      <p className="text-sm text-gray-500">
                        {document.artist_signature_date ? format(new Date(document.artist_signature_date), "MMMM d, yyyy") : ""}
                      </p>
                    </div>
                  ) : (
                    <p className="italic text-gray-400">Not yet signed</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      );
    } else if (document.content && typeof document.content === 'object') {
      // Legacy format or fallback for any document type with object content
      return (
        <div className="document-content p-8 border rounded-md bg-white shadow-sm">
          <h1 className="text-2xl font-bold text-center mb-6">{document.title}</h1>

          <div className="mb-6">
            <p className="text-sm text-gray-500 mb-2">This document is for:</p>
            <div className="flex justify-between">
              <div className="w-1/2 pr-4">
                <p className="font-semibold">Venue:</p>
                <p>{document.venue_name || "Venue Name"}</p>
                <p className="text-sm text-gray-600">{document.venue_address || "Venue Address"}</p>
              </div>
              <div className="w-1/2 pl-4">
                <p className="font-semibold">Artist:</p>
                <p>{document.artist_name || "Artist Name"}</p>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h2 className="text-lg font-semibold border-b pb-2 mb-3">Event Details</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Event:</p>
                <p>{document.booking_title || "Untitled Event"}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Date:</p>
                <p>{document.booking_start ? format(new Date(document.booking_start), "MMMM d, yyyy") : "TBD"}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Time:</p>
                <p>
                  {document.booking_start ? format(new Date(document.booking_start), "h:mm a") : "TBD"} -
                  {document.booking_end ? format(new Date(document.booking_end), "h:mm a") : "TBD"}
                </p>
              </div>
              {document.document_type === 'contract' && (
                <div>
                  <p className="text-sm text-gray-500">Compensation:</p>
                  {document.pricing_type === 'hourly' ? (
                    <div>
                      <p>
                        {formatCurrencyEU(calculateTotalPrice(
                          document.price,
                          document.booking_start,
                          document.booking_end,
                          'hourly'
                        ))}
                      </p>
                      <p className="text-xs text-gray-500">
                        ({formatCurrencyEU(document.price)}/hour ×
                        {(() => {
                          if (!document.booking_start || !document.booking_end) return 'N/A';
                          const start = new Date(document.booking_start);
                          const end = new Date(document.booking_end);
                          const hours = differenceInHours(end, start);
                          const remainingMinutes = differenceInMinutes(end, start) % 60;
                          return `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}m` : ''}`;
                        })()})
                      </p>
                    </div>
                  ) : (
                    <p>{formatCurrencyEU(document.price || 0)}</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {document.content && typeof document.content === 'object' && (
            <div className="mb-6">
              <h2 className="text-lg font-semibold border-b pb-2 mb-3">
                {document.document_type === 'contract' ? 'Terms and Conditions' :
                 document.document_type === 'rider' ? 'Technical Requirements' :
                 document.document_type === 'callsheet' ? 'Call Details' : 'Document Content'}
              </h2>
              <div className="prose prose-sm max-w-none">
                <p>{document.content.terms || document.content.details || document.content.requirements || "No content specified."}</p>
              </div>
            </div>
          )}

          {/* Show signatures section for any signable document */}
          {document.is_signable && (
            <div className="mt-12 pt-8 border-t">
              <h2 className="text-lg font-semibold mb-6 text-center">Signatures</h2>
              <div className="flex justify-between">
                <div className="w-1/2 pr-4 border-r">
                  <p className="font-semibold mb-2">For the Venue:</p>
                  {document.signed_by_venue ? (
                    <div>
                      <p className="italic text-green-600 flex items-center">
                        <CheckCircle2 className="h-4 w-4 mr-1" />
                        Signed electronically
                      </p>
                      <p className="text-sm text-gray-500">
                        {document.venue_signature_date ? format(new Date(document.venue_signature_date), "MMMM d, yyyy") : ""}
                      </p>
                    </div>
                  ) : (
                    <p className="italic text-gray-400">Not yet signed</p>
                  )}
                </div>
                <div className="w-1/2 pl-4">
                  <p className="font-semibold mb-2">For the Artist:</p>
                  {document.signed_by_artist ? (
                    <div>
                      <p className="italic text-green-600 flex items-center">
                        <CheckCircle2 className="h-4 w-4 mr-1" />
                        Signed electronically
                      </p>
                      <p className="text-sm text-gray-500">
                        {document.artist_signature_date ? format(new Date(document.artist_signature_date), "MMMM d, yyyy") : ""}
                      </p>
                    </div>
                  ) : (
                    <p className="italic text-gray-400">Not yet signed</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      );
    } else if (document.file_url) {
      const isPdf = document.file_url.toLowerCase().endsWith('.pdf');

      if (isPdf) {
        return (
          <div className={`${isMobile ? 'h-[400px]' : 'h-[600px]'} border rounded-md overflow-hidden`}>
            <iframe
              src={`${document.file_url}#toolbar=0`}
              className="w-full h-full"
              title={document.title}
            />
          </div>
        );
      } else {
        return (
          <div className="text-center py-12 border rounded-md">
            <File className="h-12 w-12 mx-auto text-gray-400 mb-2" />
            <h3 className="text-lg font-medium mb-2">Document Preview Not Available</h3>
            <p className="text-gray-500 mb-4">This file type cannot be previewed directly in the browser.</p>
            <Button onClick={handleDownload}>
              <Download className="mr-2 h-4 w-4" />
              Download to View
            </Button>
          </div>
        );
      }
    }

    return (
      <div className="text-center py-12 border rounded-md">
        <FileText className="h-12 w-12 mx-auto text-gray-400 mb-2" />
        <h3 className="text-lg font-medium">No Content Available</h3>
        <p className="text-gray-500">This document has no viewable content.</p>
      </div>
    );
  };

  if (!standalone) {
    if (loading) {
      return <div className="flex justify-center items-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>;
    }

    return renderDocumentContent();
  }

  return (
    <DashboardLayout userType={userType}>
      <div className="space-y-6">
        {loading ? (
          <div className="flex flex-col items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
            <p className="mt-4 text-gray-600">Loading document...</p>
          </div>
        ) : document ? (
          <>
            {/* Header section with responsive layout */}
            <div className={`${isMobile ? 'flex flex-col space-y-4' : 'flex items-center justify-between'}`}>
              <div className={`${isMobile ? 'flex flex-col space-y-2' : 'flex items-center space-x-4'}`}>
                <div className="flex items-center">
                  <Button variant="ghost" size="icon" onClick={() => navigate(-1)} className="mr-2">
                    <ArrowLeft className="h-5 w-5" />
                  </Button>
                  <div>
                    <h1 className="text-xl md:text-2xl font-bold tracking-tight">{document.title}</h1>
                    <div className="flex flex-wrap items-center gap-2 text-sm text-gray-500 mt-1">
                      <span>{document.document_type.charAt(0).toUpperCase() + document.document_type.slice(1)}</span>
                      <span className="hidden md:inline">•</span>
                      <span className="hidden md:inline">Updated {format(new Date(document.updated_at), 'MMM d, yyyy')}</span>
                      <Badge {...getDocumentStatusBadgeProps(document.status)}>
                        {document.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action buttons with responsive layout */}
              {isMobile ? (
                <div className="flex justify-between items-center">
                  {/* Primary action - Sign button if applicable */}
                  <div className="flex-1">
                    {document.is_signable && (
                      <Button
                        disabled={
                          signing ||
                          (isArtist && document.signed_by_artist) ||
                          (isVenue && document.signed_by_venue)
                        }
                        onClick={handleSignDocument}
                        variant={((isArtist && document.signed_by_artist) || (isVenue && document.signed_by_venue)) ? "outline" : "default"}
                        className={`w-full ${((isArtist && document.signed_by_artist) || (isVenue && document.signed_by_venue)) ? "border-green-500 text-green-600" : ""}`}
                      >
                        {signing ? (
                          <>
                            <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                            Signing...
                          </>
                        ) : ((isArtist && document.signed_by_artist) || (isVenue && document.signed_by_venue)) ? (
                          <>
                            <CheckCircle2 className="mr-1 h-4 w-4" />
                            Signed
                          </>
                        ) : (
                          <>
                            <UserCheck className="mr-1 h-4 w-4" />
                            Sign
                          </>
                        )}
                      </Button>
                    )}
                  </div>

                  {/* Secondary actions in dropdown menu */}
                  <div className="ml-2">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="icon">
                          <Menu className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={handleDownload}>
                          <Download className="mr-2 h-4 w-4" />
                          Download
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setShowShareModal(true)}>
                          <Share2 className="mr-2 h-4 w-4" />
                          Share
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ) : (
                <div className="flex gap-2">
                  {document.is_signable && (
                    <Button
                      disabled={
                        signing ||
                        (isArtist && document.signed_by_artist) ||
                        (isVenue && document.signed_by_venue)
                      }
                      onClick={handleSignDocument}
                      variant={((isArtist && document.signed_by_artist) || (isVenue && document.signed_by_venue)) ? "outline" : "default"}
                      className={((isArtist && document.signed_by_artist) || (isVenue && document.signed_by_venue)) ? "border-green-500 text-green-600" : ""}
                    >
                      {signing ? (
                        <>
                          <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                          Signing...
                        </>
                      ) : ((isArtist && document.signed_by_artist) || (isVenue && document.signed_by_venue)) ? (
                        <>
                          <CheckCircle2 className="mr-1 h-4 w-4" />
                          Signed
                        </>
                      ) : (
                        <>
                          <UserCheck className="mr-1 h-4 w-4" />
                          Sign Document
                        </>
                      )}
                    </Button>
                  )}

                  <Button variant="outline" onClick={handleDownload}>
                    <Download className="mr-1 h-4 w-4" />
                    Download
                  </Button>

                  <Button variant="outline" onClick={() => setShowShareModal(true)}>
                    <Share2 className="mr-1 h-4 w-4" />
                    Share
                  </Button>
                </div>
              )}
            </div>

            {/* Responsive layout for document content and sidebar */}
            {isMobile ? (
              <div className="space-y-6">
                {/* Document content card */}
                <Card>
                  <CardContent className="p-4">
                    <div ref={contentRef}>
                      {renderDocumentContent()}
                    </div>
                  </CardContent>
                </Card>

                {/* Document details card - Mobile version with horizontal layout */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Document Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">Created</span>
                        </div>
                        <p className="font-medium text-sm">
                          {format(new Date(document.created_at), 'MMM d, yyyy')}
                        </p>
                      </div>

                      <div>
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">Type</span>
                        </div>
                        <p className="font-medium text-sm capitalize">
                          {document.document_type}
                        </p>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">Related Booking</span>
                      </div>
                      <button
                        onClick={handleViewBooking}
                        className="font-medium text-primary hover:underline flex items-center text-sm"
                      >
                        {document.booking_title || 'Untitled Booking'}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </button>
                      {document.booking_start && (
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(document.booking_start), 'MMMM d, yyyy')}
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <div className="lg:col-span-3">
                  <Card>
                    <CardContent className="p-6">
                      <div ref={contentRef}>
                        {renderDocumentContent()}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Document Details</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">Created</span>
                        </div>
                        <p className="font-medium">
                          {format(new Date(document.created_at), 'MMMM d, yyyy')}
                        </p>
                      </div>

                      <Separator />

                      <div>
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">Type</span>
                        </div>
                        <p className="font-medium capitalize">
                          {document.document_type}
                        </p>
                      </div>

                      <div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">Related Booking</span>
                        </div>
                        <button
                          onClick={handleViewBooking}
                          className="font-medium text-primary hover:underline flex items-center"
                        >
                          {document.booking_title || 'Untitled Booking'}
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </button>
                        {document.booking_start && (
                          <p className="text-sm text-muted-foreground">
                            {format(new Date(document.booking_start), 'MMMM d, yyyy')}
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-64">
            <AlertTriangle className="h-12 w-12 text-amber-500" />
            <h3 className="mt-2 text-xl font-semibold text-gray-900">Document Not Available</h3>
            <p className="mt-1 text-gray-600 text-center max-w-md">
              The document you're looking for doesn't exist or you don't have permission to view it.
              <br />
              If you believe this is an error, please contact the document owner.
            </p>
            <div className="flex gap-4 mt-4">
              <Button variant="outline" onClick={() => navigate(-1)}>
                Go Back
              </Button>
              <Button onClick={() => loadDocument(id || '')}>
                Try Again
              </Button>
            </div>
          </div>
        )}
      </div>

      <AlertDialog open={showSignDialog} onOpenChange={setShowSignDialog}>
        <AlertDialogContent className={`${isMobile ? 'max-w-[95vw] p-4' : ''}`}>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-lg">Sign this document?</AlertDialogTitle>
            <AlertDialogDescription>
              <p className="mb-4 text-sm md:text-base">
                By signing this document, you confirm that you have read and agree to its contents.
                This action represents a legally binding electronic signature and cannot be undone.
              </p>
              <div className="bg-amber-50 border border-amber-200 rounded-md p-3 text-amber-800 text-sm">
                <p className="font-medium">You are signing as: {isArtist ? 'Artist' : 'Venue'}</p>
                <p className="mt-1">Name: {profile?.name}</p>
                <p className="mt-1">Date: {format(new Date(), 'MMMM d, yyyy')}</p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className={`${isMobile ? 'flex-col space-y-2' : ''}`}>
            <AlertDialogCancel className={`${isMobile ? 'w-full mt-2' : ''}`}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmSignDocument}
              className={`bg-primary text-primary-foreground hover:bg-primary/90 ${isMobile ? 'w-full' : ''}`}
            >
              {signing ? (
                <>
                  <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                  Signing...
                </>
              ) : (
                <>
                  <UserCheck className="mr-1 h-4 w-4" />
                  Sign Document
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {document && (
        <DocumentShareModal
          open={showShareModal}
          onClose={() => setShowShareModal(false)}
          documentId={document.id}
          documentTitle={document.title}
          shareId={document.share_id}
          isPublic={document.is_public}
        />
      )}
    </DashboardLayout>
  );
};

export default DocumentViewer;
