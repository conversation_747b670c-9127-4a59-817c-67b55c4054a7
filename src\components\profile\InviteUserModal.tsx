import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Mail, Copy, Check, Link2, Send } from 'lucide-react';
import { useToast } from '@/core';
import { supabase } from '@/core/api/supabase';
import { generateInvitationToken } from '@/features/entities/api/invitationHelpers';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface InviteUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  entityId: string;
  entityName: string;
  entityType: 'venue' | 'agency' | 'artist';
  onInvitationSent: () => void;
}

const InviteUserModal = ({
  open,
  onOpenChange,
  entityId,
  entityName,
  entityType,
  onInvitationSent
}: InviteUserModalProps) => {
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('member');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [invitationUrl, setInvitationUrl] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [showEmailForm, setShowEmailForm] = useState(true);
  const { toast } = useToast();

  const resetForm = () => {
    setEmail('');
    setRole('member');
    setEmailError('');
    setInvitationUrl(null);
    setCopied(false);
    setShowEmailForm(true);
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError('Email is required');
      return false;
    }
    if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address');
      return false;
    }
    setEmailError('');
    return true;
  };

  const handleCopyLink = () => {
    if (invitationUrl) {
      navigator.clipboard.writeText(invitationUrl);
      setCopied(true);
      toast({
        title: 'Link copied',
        description: 'Invitation link copied to clipboard',
      });

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateEmail(email)) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Check if user already exists
      const { data: existingUsers, error: userError } = await supabase
        .from('users')
        .select('id')
        .eq('email', email.toLowerCase())
        .limit(1);

      if (userError) {
        throw new Error(`Error checking existing users: ${userError.message}`);
      }

      // Check if there's already an invitation for this email and entity
      const { data: existingInvitations, error: invitationError } = await supabase
        .from('entity_invitations')
        .select('id, status')
        .eq('entity_id', entityId)
        .eq('email', email.toLowerCase())
        .eq('status', 'pending')
        .limit(1);

      if (invitationError) {
        throw new Error(`Error checking existing invitations: ${invitationError.message}`);
      }

      if (existingInvitations && existingInvitations.length > 0) {
        toast({
          title: 'Invitation already exists',
          description: `There is already a pending invitation for ${email}`,
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Check if user is already a member of this entity
      if (existingUsers && existingUsers.length > 0) {
        const userId = existingUsers[0].id;

        const { data: existingEntityUsers, error: entityUserError } = await supabase
          .from('entity_users')
          .select('id')
          .eq('entity_id', entityId)
          .eq('user_id', userId)
          .limit(1);

        if (entityUserError) {
          throw new Error(`Error checking existing entity users: ${entityUserError.message}`);
        }

        if (existingEntityUsers && existingEntityUsers.length > 0) {
          toast({
            title: 'User already a member',
            description: `${email} is already a member of this ${entityType}`,
            variant: 'destructive',
          });
          setIsSubmitting(false);
          return;
        }
      }

      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        throw new Error('Authentication error. Please try again.');
      }

      // Generate a secure token
      const token = await generateInvitationToken();

      // Calculate expiration date (7 days from now)
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);

      // Create invitation record
      const { data: invitation, error: createError } = await supabase
        .from('entity_invitations')
        .insert({
          entity_id: entityId,
          invited_by: user.id,
          email: email.toLowerCase(),
          role,
          token,
          status: 'pending',
          expires_at: expiresAt.toISOString(),
        })
        .select()
        .single();

      if (createError) {
        throw new Error(`Error creating invitation: ${createError.message}`);
      }

      // Generate the registration URL
      const registrationUrl = `${window.location.origin}/register?invitation=${token}`;
      setInvitationUrl(registrationUrl);
      setShowEmailForm(false);

      toast({
        title: 'Invitation created',
        description: `Invitation for ${email} has been created`,
      });

      // Note: Email sending is commented out as per request
      // Will be implemented later
      /*
      const { error: emailError } = await supabase.functions.invoke('send-invitation-email', {
        body: {
          email: email.toLowerCase(),
          inviterName: user.user_metadata?.name || 'A user',
          entityName,
          entityType,
          role,
          registrationUrl,
          expiresAt: expiresAt.toISOString(),
        },
      });

      if (emailError) {
        // If email fails, we should delete the invitation
        await supabase
          .from('entity_invitations')
          .delete()
          .eq('id', invitation.id);

        throw new Error(`Error sending invitation email: ${emailError.message}`);
      }
      */

    } catch (error: any) {
      console.error('Error creating invitation:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to create invitation. Please try again.',
        variant: 'destructive',
      });
      setIsSubmitting(false);
    }
  };

  const handleDone = () => {
    onInvitationSent();
    resetForm();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={(open) => {
      if (!open) resetForm();
      onOpenChange(open);
    }}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Invite User</DialogTitle>
          <DialogDescription>
            {showEmailForm
              ? `Send an invitation to join this ${entityType} as a team member.`
              : `Share this invitation link with the user.`
            }
          </DialogDescription>
        </DialogHeader>

        {showEmailForm ? (
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-right">
                  Email
                </Label>
                <div className="relative">
                  <Mail className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    id="email"
                    placeholder="<EMAIL>"
                    className="pl-8"
                    value={email}
                    onChange={(e) => {
                      setEmail(e.target.value);
                      if (emailError) validateEmail(e.target.value);
                    }}
                  />
                </div>
                {emailError && <p className="text-sm text-red-500">{emailError}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="role" className="text-right">
                  Role
                </Label>
                <Select value={role} onValueChange={setRole}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="owner">Owner</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="member">Member</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create Invitation'
                )}
              </Button>
            </DialogFooter>
          </form>
        ) : (
          <div className="py-4 space-y-4">
            <Alert>
              <AlertDescription>
                This invitation link will expire in 7 days. The user will need to create an account to join your {entityType}.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <Label>Invitation Link</Label>
              <div className="flex items-center space-x-2">
                <Input
                  value={invitationUrl || ''}
                  readOnly
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleCopyLink}
                >
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                This link is for <strong>{email}</strong> with role <strong>{role}</strong>
              </p>
            </div>

            <div className="flex flex-col space-y-2">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setShowEmailForm(true)}
                disabled={isSubmitting}
              >
                <Link2 className="mr-2 h-4 w-4" />
                Create Another Invitation
              </Button>

              <Button
                variant="default"
                className="w-full"
                onClick={handleDone}
                disabled={isSubmitting}
              >
                Done
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default InviteUserModal;
