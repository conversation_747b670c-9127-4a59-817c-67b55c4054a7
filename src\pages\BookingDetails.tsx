
import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";
import { Badge } from "@/components/ui/badge";
import {
  MapPin, FileText, Clock, CheckCircle2, XCircle,
  Upload, Mail, Share2, FileText as FileTextIcon, AlertCircle, MoreVertical,
  Eye, UserCircle, MoreHorizontal, Edit, Trash2, ChevronDown, AlertTriangle,
  Paperclip, Phone, ArrowLeft,
  Euro
} from 'lucide-react';
import { Button } from "@/components/ui/button";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { format, differenceInHours, differenceInMinutes } from "date-fns";
import { supabase, getBookingDocuments, getBookingDetails, getEntityUsers } from "@/core/api/supabase-compat";
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import DocumentUploadModal from "@/components/documents/DocumentUploadModal";
import DocumentGenerateModal from "@/components/documents/DocumentGenerateModal";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import EditBookingModal from "@/components/bookings/EditBookingModal";
import {
  formatDateEU,
  formatTimeEU,
  formatCurrencyEU,
  calculateTotalPrice,
  getDocumentStatus,
  getStatusBadgeVariant
} from "@/core";

interface Document {
  id: string;
  name: string;
  file_url: string;
  created_at: string;
  document_type: string;
  status: string;
}

interface Person {
  id: string;
  name?: string;
  email?: string;
  phone_number?: string;
  profile_image_url?: string;
  description?: string;
  role: string;
  type: 'artist' | 'venue' | 'other';
}

interface EntityUser {
  user_id: string;
  user_name: string;
  email: string;
  phone_number: string | null;
  role: string;
  is_primary: boolean;
}

interface BookingDetails {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  duration: string;
  location: string;
  price: string | number;
  pricing_type: 'fixed' | 'hourly';
  status: "pending" | "confirmed" | "canceled" | "completed";
  payment_status: "paid" | "unpaid" | "partial";
  payment_due: string;
  artist: {
    id: string;
    name?: string;
    email?: string;
    profile_image_url?: string;
    description?: string;
    genre?: string[];
    artist_name?: string;
    banner_image_url?: string;
    region?: string;
    social_links?: {
      instagram?: string;
      spotify?: string;
      soundcloud?: string;
      mixcloud?: string;
      tiktok?: string;
    };
  };
  venue: {
    id: string;
    name?: string;
    address?: string;
    email?: string;
    description?: string;
    venue_type?: string;
    venue_name?: string;
    invoice_address?: string;
    company_name?: string;
    vat_number?: string;
  };
  documents: Document[];
  booking_start: string;
  booking_end: string;
  venue_id: string;
  artist_id: string;
  created_by?: string;
  people?: Person[];
  artistUsers?: EntityUser[];
}

const BookingDetails = () => {
  const { id } = useParams<{ id: string; }>();
  const { profile } = useAuth();
  const navigate = useNavigate();
  const userType = profile?.user_type as "venue" | "artist" || "venue";

  const [booking, setBooking] = useState<BookingDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [errorMessage, setErrorMessage] = useState("");
  const [, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);

  const [showDeleteBookingDialog, setShowDeleteBookingDialog] = useState(false);
  const [showDocumentUploadModal, setShowDocumentUploadModal] = useState(false);
  const [showDocumentGenerateModal, setShowDocumentGenerateModal] = useState(false);
  // Document type is now selected in the modal
  const [showDeleteDocumentDialog, setShowDeleteDocumentDialog] = useState(false);
  const [showEditBookingModal, setShowEditBookingModal] = useState(false);

  useEffect(() => {
    if (!id) return;
    const fetchBookingDetails = async () => {
      setIsLoading(true);
      try {
        // Use the new helper function to get all booking details
        const bookingDetailsData = await getBookingDetails(id).catch(error => {
          console.error("Error in getBookingDetails:", error);
          throw new Error(error.message || 'Failed to fetch booking details');
        });

        const bookingData = bookingDetailsData.booking;
        const artistDetails = bookingDetailsData.artist.details;
        const artistProfileData = bookingDetailsData.artist.profile;
        const venueDetails = bookingDetailsData.venue.details;
        const venueProfileData = bookingDetailsData.venue.profile;
        const creatorData = bookingDetailsData.creator;

        // Get documents
        const documentsData = await getBookingDocuments(id);

        // Calculate duration
        const startTime = new Date(bookingData.booking_start);
        const endTime = new Date(bookingData.booking_end);
        const hours = differenceInHours(endTime, startTime);
        const remainingMinutes = differenceInMinutes(endTime, startTime) % 60;
        const durationText = `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}m` : ''}`;

        // Prepare people data for the people card
        const people: Person[] = [];

        // Add the artist with contact info
        people.push({
          id: bookingData.artist_id,
          name: artistDetails?.artist_name || artistProfileData?.name || 'Unknown Artist',
          email: artistProfileData?.email || null,
          phone_number: artistProfileData?.phone_number || null,
          profile_image_url: artistDetails?.profile_image_url || null,
          description: null,
          role: 'Performing Artist',
          type: 'artist'
        });

        // Add the booking creator if available
        if (creatorData) {
          people.push({
            id: creatorData.id,
            name: creatorData.name || 'Unknown User',
            email: creatorData.email || null,
            phone_number: null, // Don't expose phone number
            role: 'Booking Manager',
            type: 'venue'
          });
        }

        const transformedBooking: BookingDetails = {
          id: bookingData.id,
          title: bookingData.title,
          description: bookingData.description || "",
          date: formatDateEU(new Date(bookingData.booking_start)),
          time: `${formatTimeEU(new Date(bookingData.booking_start))} - ${formatTimeEU(new Date(bookingData.booking_end))}`,
          duration: durationText,
          location: bookingData.location || "Main Stage",
          price: bookingData.price.toString(),
          pricing_type: bookingData.pricing_type as 'fixed' | 'hourly',
          status: bookingData.status as "pending" | "confirmed" | "canceled" | "completed",
          payment_status: "unpaid",
          payment_due: formatDateEU(new Date()),
          booking_start: bookingData.booking_start,
          booking_end: bookingData.booking_end,
          venue_id: bookingData.venue_id,
          artist_id: bookingData.artist_id,
          created_by: bookingData.created_by,
          people: people,
          artist: {
            id: bookingData.artist_id,
            name: artistDetails?.artist_name || artistProfileData?.name || 'Unknown Artist',
            email: artistProfileData?.email || null,
            artist_name: artistDetails?.artist_name || 'Unknown Artist',
            profile_image_url: artistDetails?.profile_image_url || null,
            description: null,
            genre: null,
            banner_image_url: null,
            region: null,
            social_links: null
          },
          venue: {
            id: bookingData.venue_id,
            name: venueProfileData?.name || 'Unknown Venue',
            venue_name: venueDetails?.venue_name || 'Unknown Venue',
            address: venueDetails?.address || null,
            email: venueProfileData?.email || null,
            description: null,
            venue_type: null,
            invoice_address: null,
            company_name: null,
            vat_number: null
          },
          documents: []
        };

        const transformedDocuments: Document[] = documentsData ? documentsData.map(doc => ({
          id: doc.id,
          name: doc.title,
          file_url: doc.file_url || "",
          created_at: doc.created_at,
          document_type: doc.document_type,
          status: doc.status
        })) : [];

        // Fetch artist users
        let artistEntityUsers = [];
        try {
          artistEntityUsers = await getEntityUsers(bookingData.artist_id) || [];
        } catch (error) {
          console.error("Error fetching artist users:", error);
          // Continue with empty array
        }

        setBooking({
          ...transformedBooking,
          documents: transformedDocuments,
          artistUsers: artistEntityUsers
        });
        setDocuments(transformedDocuments);
      } catch (error: any) {
        console.error("Error fetching booking details:", error);
        setErrorMessage(error.message);
        toast.error("Failed to load booking details");
      } finally {
        setIsLoading(false);
      }
    };
    fetchBookingDetails();
  }, [id]);

  // No need to track existing contract types anymore

  const refreshDocuments = async () => {
    if (!id) return;
    try {
      const documentsData = await getBookingDocuments(id);
      const transformedDocuments: Document[] = documentsData ? documentsData.map(doc => ({
        id: doc.id,
        name: doc.title,
        file_url: doc.file_url || "",
        created_at: doc.created_at,
        document_type: doc.document_type,
        status: doc.status
      })) : [];
      setDocuments(transformedDocuments);
      // No need to track existing contract types anymore
    } catch (error) {
      console.error("Error fetching documents:", error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return "bg-green-500 text-white";
      case "pending":
        return "bg-yellow-500 text-white";
      case "completed":
        return "bg-blue-500 text-white";
      case "canceled":
        return "bg-red-500 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };



  const handleStatusChange = async (status: "confirmed" | "pending" | "canceled" | "completed") => {
    if (!booking) return;
    setIsSaving(true);
    try {
      const { error } = await supabase.from("bookings").update({ status }).eq("id", booking.id);
      if (error) throw error;
      setBooking(prev => prev ? { ...prev, status } : null);
      toast.success(`Booking status updated to ${status}`);
    } catch (error: any) {
      console.error("Error updating booking status:", error);
      toast.error("Failed to update booking status");
    } finally {
      setIsSaving(false);
    }
  };

  const handleBookingUpdated = () => {
    if (id) {
      // Refresh all booking details when a booking is updated
      const fetchUpdatedBookingDetails = async () => {
        try {
          // Use the new helper function to get all booking details
          const bookingDetailsData = await getBookingDetails(id);

          if (!bookingDetailsData) {
            throw new Error('Failed to fetch updated booking details');
          }

          const bookingData = bookingDetailsData.booking;
          const artistDetails = bookingDetailsData.artist.details;
          const artistProfileData = bookingDetailsData.artist.profile;

          // Update booking state with refreshed data
          setBooking(prevBooking => {
            if (!prevBooking) return null;

            // Update people array with refreshed artist data
            const updatedPeople = prevBooking.people?.map(person => {
              if (person.type === 'artist') {
                return {
                  ...person,
                  name: artistDetails?.artist_name || artistProfileData?.name || person.name,
                  email: artistProfileData?.email || person.email,
                  phone_number: null,
                  profile_image_url: artistDetails?.profile_image_url || person.profile_image_url,
                  description: person.description
                };
              }
              return person;
            });

            return {
              ...prevBooking,
              title: bookingData.title,
              description: bookingData.description || "",
              date: formatDateEU(new Date(bookingData.booking_start)),
              time: `${formatTimeEU(new Date(bookingData.booking_start))} - ${formatTimeEU(new Date(bookingData.booking_end))}`,
              location: bookingData.location,
              price: bookingData.price.toString(),
              pricing_type: bookingData.pricing_type as 'fixed' | 'hourly',
              booking_start: bookingData.booking_start,
              booking_end: bookingData.booking_end,
              people: updatedPeople,
              artist: {
                ...prevBooking.artist,
                name: artistDetails?.artist_name || artistProfileData?.name || prevBooking.artist.name,
                email: artistProfileData?.email || prevBooking.artist.email,
                artist_name: artistDetails?.artist_name || prevBooking.artist.artist_name,
                profile_image_url: artistDetails?.profile_image_url || prevBooking.artist.profile_image_url,
                banner_image_url: prevBooking.artist.banner_image_url,
                description: prevBooking.artist.description,
                genre: prevBooking.artist.genre,
                region: prevBooking.artist.region,
                social_links: prevBooking.artist.social_links
              }
            };
          });
          toast.success("Booking updated successfully");
        } catch (error) {
          console.error("Error refreshing booking details:", error);
        }
      };
      fetchUpdatedBookingDetails();
    }
  };

  const handleEditBookingClick = () => {
    setShowEditBookingModal(true);
  };

  const handleDeleteBookingClick = () => {
    setShowDeleteBookingDialog(true);
  };

  const handleDeleteBooking = async () => {
    if (!booking) return;
    try {
      setIsDeleting(true);
      const { data: documents, error: docError } =
        await supabase.from("documents").select("id, file_url").eq("booking_id", booking.id);

      if (docError) {
        console.error("Error checking for documents:", docError);
        throw new Error("Failed to check for associated documents");
      }

      if (documents && documents.length > 0) {
        (`Deleting ${documents.length} documents associated with booking ${booking.id}`);
        for (const doc of documents) {
          const { error: deleteDocError } = await supabase.from("documents").delete().eq("id", doc.id);

          if (deleteDocError) {
            console.error(`Error deleting document ${doc.id}:`, deleteDocError);
            continue;
          }

          if (doc.file_url) {
            try {
              const filePath = doc.file_url.split('/').pop();
              if (filePath) {
                await supabase.storage.from('documents').remove([`${booking.id}/${filePath}`]);
              }
            } catch (storageError) {
              console.error("Error deleting file from storage:", storageError);
            }
          }
        }
      }

      const { error } = await supabase.from("bookings").delete().eq("id", booking.id);
      if (error) {
        console.error("Error deleting booking:", error);
        throw error;
      }

      toast.success("Booking deleted successfully");
      window.location.href = userType === "venue" ? "/venue/bookings" : "/artist/bookings";
    } catch (error: any) {
      console.error("Error deleting booking:", error);
      toast.error("Failed to delete booking: " + (error.message || "Unknown error"));
    } finally {
      setIsDeleting(false);
      setShowDeleteBookingDialog(false);
    }
  };

  const handleDeleteDocumentClick = (documentId: string) => {
    setDocumentToDelete(documentId);
    setShowDeleteDocumentDialog(true);
  };

  const handleDeleteDocument = async () => {
    if (!documentToDelete) return;
    try {
      setIsDeleting(true);
      const { data: docCheck, error: checkError } =
        await supabase.from("documents").select("id, file_url").eq("id", documentToDelete).single();

      if (checkError) {
        console.error("Error checking document:", checkError);
        throw new Error("Document not found");
      }

      const { error } = await supabase.from("documents").delete().eq("id", documentToDelete);
      if (error) {
        console.error("Error in delete operation:", error);
        throw error;
      }

      if (docCheck.file_url && id) {
        try {
          const filePath = docCheck.file_url.split('/').pop();
          if (filePath) {
            await supabase.storage.from('documents').remove([`${id}/${filePath}`]);
          }
        } catch (storageError) {
          console.error("Error deleting file from storage:", storageError);
        }
      }

      await refreshDocuments();
      toast.success("Document deleted successfully");
    } catch (error: any) {
      console.error("Error deleting document:", error);
      toast.error("Failed to delete document: " + (error.message || "Unknown error"));
    } finally {
      setIsDeleting(false);
      setShowDeleteDocumentDialog(false);
      setDocumentToDelete(null);
    }
  };

  const handleGenerateDocument = () => {
    if (id) {
      setShowDocumentGenerateModal(true);
    }
  };

  const handleCloseDocumentGenerateModal = () => {
    setShowDocumentGenerateModal(false);
    refreshDocuments();
  };

  if (isLoading) {
    return <DashboardLayout userType={userType}>
        <div className="flex flex-col items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          <p className="mt-4 text-gray-600">Loading booking details...</p>
        </div>
      </DashboardLayout>;
  }

  if (errorMessage) {
    return <DashboardLayout userType={userType}>
        <div className="flex flex-col items-center justify-center h-64">
          <AlertTriangle className="h-12 w-12 text-red-500" />
          <h3 className="mt-2 text-xl font-semibold text-gray-900">Error Loading Booking</h3>
          <p className="mt-1 text-gray-600">{errorMessage}</p>
          <Button className="mt-4" asChild>
            <Link to={userType === "venue" ? "/venue/bookings" : "/artist/bookings"}>Back to Bookings</Link>
          </Button>
        </div>
      </DashboardLayout>;
  }

  if (!booking) {
    return <DashboardLayout userType={userType}>
        <div className="flex flex-col items-center justify-center h-64">
          <AlertTriangle className="h-12 w-12 text-yellow-500" />
          <h3 className="mt-2 text-xl font-semibold text-gray-900">Booking Not Found</h3>
          <p className="mt-1 text-gray-600">
            The booking you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Button className="mt-4" asChild>
            <Link to={userType === "venue" ? "/venue/bookings" : "/artist/bookings"}>Back to Bookings</Link>
          </Button>
        </div>
      </DashboardLayout>;
  }

  return <DashboardLayout userType={userType}>
      <DeleteConfirmationDialog
        open={showDeleteBookingDialog}
        onOpenChange={setShowDeleteBookingDialog}
        onConfirm={handleDeleteBooking}
        title="Delete Booking"
        description={`Are you sure you want to delete the booking "${booking?.title}"? This will also delete all associated documents and cannot be undone.`}
        itemType="booking"
        isDeleting={isDeleting}
      />

      <DeleteConfirmationDialog
        open={showDeleteDocumentDialog}
        onOpenChange={setShowDeleteDocumentDialog}
        onConfirm={handleDeleteDocument}
        title="Delete Document"
        description="Are you sure you want to delete this document? This action cannot be undone."
        itemType="document"
        isDeleting={isDeleting}
      />

      <EditBookingModal
        open={showEditBookingModal}
        onClose={() => setShowEditBookingModal(false)}
        booking={booking ? {
          ...booking,
          price: typeof booking.price === 'string' ? parseFloat(booking.price) : booking.price
        } : null}
        userType={userType}
        onBookingUpdated={handleBookingUpdated}
      />

      <div className="space-y-6">
        <div className="flex flex-col space-y-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold tracking-tight">{booking.title}</h1>
                <p className="text-muted-foreground">Booking ID: {booking.id}</p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className={`${getStatusColor(booking.status)}`}>
                    {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    className="cursor-pointer"
                    disabled={booking.status === "pending"}
                    onClick={() => handleStatusChange("pending")}
                  >
                    <AlertCircle className="mr-2 h-4 w-4 text-yellow-500" />
                    Pending
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    disabled={booking.status === "confirmed"}
                    onClick={() => handleStatusChange("confirmed")}
                  >
                    <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                    Confirm
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    disabled={booking.status === "completed"}
                    onClick={() => handleStatusChange("completed")}
                  >
                    <CheckCircle2 className="mr-2 h-4 w-4 text-blue-500" />
                    Complete
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    disabled={booking.status === "canceled"}
                    onClick={() => handleStatusChange("canceled")}
                  >
                    <XCircle className="mr-2 h-4 w-4 text-red-500" />
                    Cancel
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem className="cursor-pointer" onClick={handleEditBookingClick}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem className="cursor-pointer">
                    <Share2 className="mr-2 h-4 w-4" />
                    Share
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="cursor-pointer text-red-600" onClick={handleDeleteBookingClick}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent">
                <CardTitle>Event Details</CardTitle>
              </CardHeader>

              <CardContent className="p-0">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-0 divide-y sm:divide-y-0 sm:divide-x divide-gray-100">
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Event</p>
                    <p className="font-medium">{booking.title}</p>
                  </div>
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Date</p>
                    <p className="font-medium">{booking.date}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-0 divide-y sm:divide-y-0 sm:divide-x divide-gray-100 border-t">
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Time</p>
                    <p className="font-medium">{booking.time}</p>
                  </div>
                  <div className="p-6">
                    <p className="text-sm text-gray-500 mb-1">Address</p>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <p className="font-medium">
                        {booking.location || booking.venue?.address || "No address provided"}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-0 divide-y divide-gray-100 border-t">

                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent">
                <CardTitle>Description</CardTitle>
              </CardHeader>

              <CardContent className="p-6">
                {booking.description ? (
                  <p className="text-gray-700">{booking.description}</p>
                ) : (
                  <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-md">
                    <FileTextIcon className="h-10 w-10 text-gray-300 mx-auto mb-2" />
                    <p className="text-gray-500">No description provided</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="border-b flex flex-row items-center justify-between bg-transparent">
                <CardTitle>Documents</CardTitle>
                <div className="flex space-x-2">
                  <Button size="sm" variant="secondary" onClick={handleGenerateDocument}>
                    <FileText className="h-4 w-4 mr-2" /> New Document
                  </Button>
                  <Button size="sm" onClick={() => setShowDocumentUploadModal(true)}>
                    <Upload className="h-4 w-4 mr-2" /> Add Document
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="p-0">
                {documents.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">#</TableHead>
                        <TableHead>Document</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {documents.map((doc, index) => (
                        <TableRow
                          key={doc.id}
                          className="cursor-pointer hover:bg-muted/50 transition-colors"
                          onClick={() => navigate(`/documents/${doc.id}`)}
                        >
                          <TableCell className="font-medium">{index + 1}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Paperclip className="h-4 w-4 text-gray-500" />
                              <span>
                                {doc.name}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="capitalize">{doc.document_type}</TableCell>
                          <TableCell>{format(new Date(doc.created_at), "MMM d, yyyy")}</TableCell>
                          <TableCell>
                            <Badge variant={getStatusBadgeVariant(doc.status)}>
                              {getDocumentStatus(doc)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  onClick={(e) => e.stopPropagation()} // Prevent row click when clicking the menu
                                >
                                  <span className="sr-only">Open menu</span>
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/documents/${doc.id}`);
                                }}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View Document</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteDocumentClick(doc.id);
                                  }}
                                  className="text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  <span>Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-12 border-2 border-dashed border-gray-200 m-6 rounded-md">
                    <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="font-medium text-lg mb-1">No documents yet</h3>
                    <p className="text-gray-500 mb-4">Upload contracts, riders, or other paperwork related to this booking</p>
                    <div className="flex justify-center gap-2">
                      <Button variant="secondary" onClick={handleGenerateDocument}>
                        <FileText className="h-4 w-4 mr-2" />
                        Create Document
                      </Button>
                      <Button onClick={() => setShowDocumentUploadModal(true)}>
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Document
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent">
                <CardTitle>Financial</CardTitle>
              </CardHeader>

              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Total Amount</p>
                    {booking.pricing_type === 'hourly' ? (
                      <>
                        <p className="text-3xl font-bold">
                          {formatCurrencyEU(calculateTotalPrice(
                            booking.price,
                            booking.pricing_type,
                            booking.booking_start,
                            booking.booking_end
                          ))}
                        </p>
                        <p className="text-sm text-gray-500">Excl. taxes</p>
                      </>
                    ) : (
                      <>
                        <p className="text-3xl font-bold">{formatCurrencyEU(booking?.price || "0")}</p>
                        <p className="text-sm text-gray-500">Excl. taxes</p>
                      </>
                    )}
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm text-gray-500 mb-1">Pricing Details</p>
                    <div className="flex items-center">
                      <Euro className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="font-medium">Type: {booking?.pricing_type === 'hourly' ? 'Hourly Rate' : 'Fixed Rate'}</span>
                    </div>
                    {booking.pricing_type === 'hourly' && (
                      <div className="flex items-center mt-1">
                        <Clock className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="font-medium">
                          Rate: {formatCurrencyEU(booking.price)}/hour • Duration: {booking.duration}
                        </span>
                      </div>
                    )}
                  </div>

                  {booking?.payment_status !== "paid"}
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent p-0">
                {booking.artist.banner_image_url && (
                  <div className="relative w-full h-32 overflow-hidden">
                    <img
                      src={booking.artist.banner_image_url}
                      alt={booking.artist.artist_name || booking.artist.name || "Artist"}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <div className="p-4 flex justify-between items-center">
                  <CardTitle>Artist Profile</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                {/* Artist Profile Section */}
                <div className="p-6 border-b">
                  <div className="flex flex-col space-y-4">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-16 w-16 border-2 border-white shadow-md">
                        {booking.artist.profile_image_url ? (
                          <AvatarImage src={booking.artist.profile_image_url} alt={booking.artist.artist_name || booking.artist.name || "Artist"} />
                        ) : (
                          <AvatarFallback className="bg-purple-100 text-purple-800">
                            {(booking.artist.artist_name || booking.artist.name || "A").charAt(0)}
                          </AvatarFallback>
                        )}
                      </Avatar>
                      <div>
                        <p className="font-medium text-xl">{booking.artist.artist_name || booking.artist.name || "Unknown Artist"}</p>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {booking.artist.genre && booking.artist.genre.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {booking.artist.genre.map((genre, index) => (
                                <Badge key={index} variant="outline" className="bg-purple-50 text-purple-800 text-[10px] px-1.5 py-0 h-4">
                                  {genre}
                                </Badge>
                              ))}
                            </div>
                          )}
                          {booking.artist.region && (
                            <Badge variant="outline" className="bg-blue-50 text-blue-800 text-[10px] px-1.5 py-0 h-4">
                              {booking.artist.region}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Social Media Links */}
                    {booking.artist.social_links && (
                      <div className="flex items-center gap-2 mt-2">
                        {booking.artist.social_links.instagram && (
                          <a href={booking.artist.social_links.instagram} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-purple-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                              <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                              <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                            </svg>
                          </a>
                        )}
                        {booking.artist.social_links.spotify && (
                          <a href={booking.artist.social_links.spotify} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-green-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="10"></circle>
                              <path d="M8 14.5c2.5-1 5.5-1 8 0"></path>
                              <path d="M6.5 12c3.5-1 7.5-1 11 0"></path>
                              <path d="M5 9.5c4.5-1 9.5-1 14 0"></path>
                            </svg>
                          </a>
                        )}
                        {booking.artist.social_links.soundcloud && (
                          <a href={booking.artist.social_links.soundcloud} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-orange-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M2 12h1"></path>
                              <path d="M5 12h1"></path>
                              <path d="M8 12h1"></path>
                              <path d="M11 12h1"></path>
                              <path d="M14 12h1"></path>
                              <path d="M17 12h1"></path>
                              <path d="M20 12h1"></path>
                              <path d="M3 9v6"></path>
                              <path d="M6 8v8"></path>
                              <path d="M9 6v12"></path>
                              <path d="M12 5v14"></path>
                              <path d="M15 5v14"></path>
                              <path d="M18 8v8"></path>
                              <path d="M21 9v6"></path>
                            </svg>
                          </a>
                        )}
                        {booking.artist.social_links.mixcloud && (
                          <a href={booking.artist.social_links.mixcloud} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-blue-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M4 14h1"></path>
                              <path d="M9 14h1"></path>
                              <path d="M14 14h1"></path>
                              <path d="M19 14h1"></path>
                              <path d="M6.5 12a5 5 0 1 1 11 0 5 5 0 0 1-11 0z"></path>
                            </svg>
                          </a>
                        )}
                        {booking.artist.social_links.tiktok && (
                          <a href={booking.artist.social_links.tiktok} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-black">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M9 12a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"></path>
                              <path d="M15 8a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"></path>
                              <path d="M15 8v8a4 4 0 0 1-4 4"></path>
                              <line x1="15" y1="4" x2="15" y2="12"></line>
                            </svg>
                          </a>
                        )}
                      </div>
                    )}


                  </div>
                </div>

                {/* Artist Users Section */}
                <div className="divide-y divide-gray-100">
                  {booking.artistUsers && booking.artistUsers.length > 0 ? (
                    booking.artistUsers.map((user) => (
                      <div key={user.user_id} className="p-4">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="bg-purple-100 text-purple-800">
                              {user.user_name?.charAt(0) || 'U'}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-1.5 flex-wrap">
                              <h3 className="font-medium text-sm">{user.user_name}</h3>



                            </div>

                            {/* Contact info inline */}
                            <div className="flex flex-wrap gap-3 mt-1 text-xs text-gray-500">
                              <div className="flex items-center gap-1">
                                <Mail className="h-3 w-3" />
                                <span className="truncate">{user.email || 'No email available'}</span>
                              </div>
                              {user.phone_number && (
                                <div className="flex items-center gap-1">
                                  <Phone className="h-3 w-3" />
                                  <span>{user.phone_number}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4">
                      <UserCircle className="h-8 w-8 text-gray-300 mx-auto mb-1" />
                      <p className="text-gray-500 text-sm">No artist contacts found</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {showDocumentUploadModal && id && (
        <DocumentUploadModal
          open={showDocumentUploadModal}
          onClose={() => setShowDocumentUploadModal(false)}
          bookingId={id}
          onSuccess={() => refreshDocuments()}
        />
      )}

      {showDocumentGenerateModal && id && (
        <DocumentGenerateModal
          open={showDocumentGenerateModal}
          onClose={handleCloseDocumentGenerateModal}
          bookingId={id}
          onSuccess={() => refreshDocuments()}
        />
      )}
    </DashboardLayout>;
};

export default BookingDetails;
