// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4'
import { corsHeaders } from '../_shared/cors.ts'

interface EmailPayload {
  email: string;
  inviterName: string;
  entityName: string;
  entityType: string;
  role: string;
  registrationUrl: string;
  expiresAt: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { email, inviterName, entityName, entityType, role, registrationUrl, expiresAt } = await req.json() as EmailPayload

    // Create a Supabase client with the Auth context of the function
    const supabaseClient = createClient(
      // Supabase API URL - env var exported by default.
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase API ANON KEY - env var exported by default.
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      // Create client with Auth context of the user that called the function.
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    )

    // Format the expiration date
    const expirationDate = new Date(expiresAt);
    const formattedDate = expirationDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Capitalize the first letter of the entity type
    const capitalizedEntityType = entityType.charAt(0).toUpperCase() + entityType.slice(1);

    // Prepare the email content
    const subject = `Invitation to join ${entityName} on Stagecloud`;
    const content = `
      <h1>You've been invited to join ${entityName}</h1>
      <p>${inviterName} has invited you to join ${entityName} as a ${role} on Stagecloud.</p>
      <p>${capitalizedEntityType} Description: ${entityName} is a ${entityType} on the Stagecloud platform.</p>
      <p>Your role will be: <strong>${role}</strong></p>
      <p>This invitation will expire on ${formattedDate}.</p>
      <p>
        <a href="${registrationUrl}" style="display: inline-block; background-color: #000; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">
          Accept Invitation
        </a>
      </p>
      <p>Or copy and paste this URL into your browser:</p>
      <p>${registrationUrl}</p>
      <p>If you did not expect this invitation, you can safely ignore this email.</p>
    `;

    // Send the email using Supabase's built-in email service
    const { error } = await supabaseClient.functions.invoke('send-email', {
      body: {
        to: email,
        subject,
        html: content,
      },
    });

    if (error) {
      throw error;
    }

    return new Response(
      JSON.stringify({ success: true }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400 
      }
    );
  }
})
